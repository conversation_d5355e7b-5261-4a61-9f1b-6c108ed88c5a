# 核心依赖
asyncio-mqtt==0.13.0
aiohttp>=3.9.0
websockets>=12.0.0

# 交易所API - CCXT Pro支持
ccxt==4.0.103
# 注意：ccxt pro包含在ccxt中，使用 import ccxt.pro

# 数据处理
numpy>=1.26.0
pandas>=2.1.0

# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
jinja2==3.1.2
python-multipart==0.0.6

# 日志和配置
structlog==23.2.0
pyyaml==6.0.1
python-dotenv==1.0.0

# 系统监控
psutil==5.9.6

# 数据验证
pydantic==2.4.2

# 时间处理
python-dateutil==2.8.2

# 测试框架
pytest==7.4.3
pytest-asyncio==0.21.1

# 开发工具
black==23.9.1
flake8==6.1.0
mypy==1.6.1

# 网络请求
requests==2.31.0

# 加密
cryptography>=42.0.0

# 数据格式
orjson==3.9.9

# 时区处理
pytz==2023.3

# HTTP客户端
httpx==0.25.2

# WebSocket客户端
websocket-client==1.6.4

# 数据处理和指标计算
scipy>=1.11.4

# Web界面
colorlog>=6.7.0

# 配置和工具
click>=8.1.0
rich>=13.5.0

# 类型检查和代码质量
isort>=5.12.0

# 数据库相关
aiosqlite==0.19.0

# 注释：以下是可选依赖，根据需要安装
# Lighter SDK - 现在启用真实数据获取
git+https://github.com/elliottech/lighter-python.git

# TA-Lib技术分析库 (可选，需要系统级安装)
# ta-lib==0.4.25 